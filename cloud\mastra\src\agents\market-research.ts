/**
 * Market Research Agent
 * 
 * Specializes in market analysis, competitive research, and business intelligence
 * using Tavily search and Firecrawl web scraping capabilities.
 */

import { Agent } from '@mastra/core';
import { Logger } from '@mastra/loggers';
import { MCPClient } from '@mastra/mcp';
import { SynthesisRequest } from '../synthesis/router.js';

export interface MarketResearchContext {
  industry?: string;
  targetMarket?: string;
  competitors?: string[];
  products?: string[];
  geographicScope?: string[];
  timeframe?: string;
  researchDepth?: 'basic' | 'detailed' | 'comprehensive';
  includeFinancials?: boolean;
  includeTrends?: boolean;
}

export interface MarketResearchResult {
  marketOverview: MarketOverview;
  competitorAnalysis: CompetitorAnalysis[];
  marketTrends: MarketTrend[];
  opportunities: MarketOpportunity[];
  threats: MarketThreat[];
  recommendations: MarketRecommendation[];
  sources: ResearchSource[];
  metadata: ResearchMetadata;
}

export interface MarketOverview {
  marketSize: string;
  growthRate: string;
  keySegments: string[];
  majorPlayers: string[];
  marketDynamics: string[];
  regulatoryEnvironment: string[];
}

export interface CompetitorAnalysis {
  name: string;
  website: string;
  description: string;
  marketShare: string;
  strengths: string[];
  weaknesses: string[];
  products: ProductInfo[];
  pricing: PricingInfo[];
  marketingStrategy: string[];
  recentNews: NewsItem[];
}

export interface ProductInfo {
  name: string;
  description: string;
  features: string[];
  targetAudience: string;
  pricing: string;
  launchDate?: string;
}

export interface PricingInfo {
  tier: string;
  price: string;
  features: string[];
  targetSegment: string;
}

export interface MarketTrend {
  trend: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  timeframe: string;
  drivers: string[];
  implications: string[];
}

export interface MarketOpportunity {
  opportunity: string;
  description: string;
  marketSize: string;
  difficulty: 'low' | 'medium' | 'high';
  timeToMarket: string;
  requiredResources: string[];
}

export interface MarketThreat {
  threat: string;
  description: string;
  probability: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  mitigationStrategies: string[];
}

export interface MarketRecommendation {
  category: string;
  recommendation: string;
  rationale: string;
  priority: 'low' | 'medium' | 'high';
  timeline: string;
  expectedOutcome: string;
}

export interface NewsItem {
  title: string;
  date: string;
  source: string;
  summary: string;
  relevance: 'high' | 'medium' | 'low';
}

export interface ResearchSource {
  url: string;
  title: string;
  type: 'website' | 'article' | 'report' | 'news';
  credibility: 'high' | 'medium' | 'low';
  lastUpdated: string;
}

export interface ResearchMetadata {
  researchDate: string;
  dataFreshness: string;
  confidenceLevel: 'high' | 'medium' | 'low';
  limitationsAndCaveats: string[];
}

/**
 * Market Research Agent - Specialized for market analysis and competitive intelligence
 */
export class MarketResearchAgent extends Agent {
  private mcpClient: MCPClient;
  private logger: Logger;
  private cache = new Map<string, any>();
  private cacheExpiry = new Map<string, number>();

  constructor(mcpClient: MCPClient, logger: Logger) {
    super({
      name: 'MarketResearchAgent',
      description: 'Specialized agent for market analysis and competitive research',
      instructions: `
        You are a market research specialist focused on:
        1. Market size and growth analysis using real-time web data
        2. Competitive landscape mapping and analysis
        3. Industry trend identification and impact assessment
        4. Market opportunity and threat analysis
        5. Strategic recommendations based on market intelligence
        
        Always provide data-driven insights with credible sources.
        Focus on actionable intelligence and strategic implications.
        Include confidence levels and data limitations in your analysis.
      `
    });
    
    this.mcpClient = mcpClient;
    this.logger = logger;
  }

  /**
   * Synthesize market research request
   */
  async synthesize(request: SynthesisRequest): Promise<MarketResearchResult> {
    this.logger.info('Starting market research synthesis', { 
      operation: request.operation,
      tools: request.tools 
    });

    const context = request.context as MarketResearchContext;
    const cacheKey = this.generateCacheKey(request);

    // Check cache first (shorter expiry for market data)
    if (this.isCacheValid(cacheKey)) {
      this.logger.info('Returning cached market research result');
      return this.cache.get(cacheKey);
    }

    try {
      const result: MarketResearchResult = {
        marketOverview: {
          marketSize: '',
          growthRate: '',
          keySegments: [],
          majorPlayers: [],
          marketDynamics: [],
          regulatoryEnvironment: []
        },
        competitorAnalysis: [],
        marketTrends: [],
        opportunities: [],
        threats: [],
        recommendations: [],
        sources: [],
        metadata: {
          researchDate: new Date().toISOString(),
          dataFreshness: 'current',
          confidenceLevel: 'medium',
          limitationsAndCaveats: []
        }
      };

      // Step 1: Market Overview Research
      result.marketOverview = await this.researchMarketOverview(context);

      // Step 2: Competitor Analysis
      if (context.competitors?.length) {
        result.competitorAnalysis = await this.analyzeCompetitors(context.competitors, context);
      }

      // Step 3: Market Trends Analysis
      if (context.includeTrends) {
        result.marketTrends = await this.identifyMarketTrends(context);
      }

      // Step 4: Opportunity and Threat Analysis
      result.opportunities = await this.identifyOpportunities(context, result);
      result.threats = await this.identifyThreats(context, result);

      // Step 5: Generate Recommendations
      result.recommendations = await this.generateRecommendations(context, result);

      // Cache the result (shorter expiry for market data)
      this.cacheResult(cacheKey, result, 15); // 15 minutes for market data

      this.logger.info('Market research synthesis completed', {
        competitorsAnalyzed: result.competitorAnalysis.length,
        trendsIdentified: result.marketTrends.length,
        opportunitiesFound: result.opportunities.length,
        threatsIdentified: result.threats.length
      });

      return result;

    } catch (error) {
      this.logger.error('Error in market research synthesis', { error });
      throw error;
    }
  }

  /**
   * Research market overview using Tavily search
   */
  private async researchMarketOverview(context: MarketResearchContext): Promise<MarketOverview> {
    try {
      const searchQuery = this.buildMarketOverviewQuery(context);
      const searchResults = await this.performTavilySearch(searchQuery);
      
      return this.parseMarketOverview(searchResults, context);
    } catch (error) {
      this.logger.warn('Failed to research market overview', { error });
      return this.createFallbackMarketOverview(context);
    }
  }

  /**
   * Analyze competitors using web scraping and search
   */
  private async analyzeCompetitors(
    competitors: string[], 
    context: MarketResearchContext
  ): Promise<CompetitorAnalysis[]> {
    const analyses: CompetitorAnalysis[] = [];

    for (const competitor of competitors) {
      try {
        // Search for competitor information
        const searchQuery = `${competitor} company analysis market share products pricing`;
        const searchResults = await this.performTavilySearch(searchQuery);
        
        // Scrape competitor website if available
        const websiteData = await this.scrapeCompetitorWebsite(competitor);
        
        const analysis = await this.parseCompetitorData(competitor, searchResults, websiteData);
        analyses.push(analysis);
        
      } catch (error) {
        this.logger.warn(`Failed to analyze competitor ${competitor}`, { error });
        analyses.push(this.createFallbackCompetitorAnalysis(competitor));
      }
    }

    return analyses;
  }

  /**
   * Perform Tavily search
   */
  private async performTavilySearch(query: string): Promise<any> {
    try {
      const tools = await this.mcpClient.getTools();
      const tavilyTools = tools.tavily;
      
      if (tavilyTools && tavilyTools['tavily-search']) {
        const result = await this.mcpClient.callTool('tavily', 'tavily-search', {
          query,
          max_results: 10,
          search_depth: 'advanced',
          include_raw_content: true
        });
        
        return result;
      }
    } catch (error) {
      this.logger.warn(`Tavily search failed for query: ${query}`, { error });
    }
    
    return null;
  }

  /**
   * Scrape competitor website using Firecrawl
   */
  private async scrapeCompetitorWebsite(competitor: string): Promise<any> {
    try {
      // First, search for the competitor's website
      const websiteQuery = `${competitor} official website`;
      const searchResults = await this.performTavilySearch(websiteQuery);
      
      if (searchResults?.results?.[0]?.url) {
        const websiteUrl = searchResults.results[0].url;
        
        const tools = await this.mcpClient.getTools();
        const firecrawlTools = tools.firecrawl;
        
        if (firecrawlTools && firecrawlTools['firecrawl_scrape']) {
          const result = await this.mcpClient.callTool('firecrawl', 'firecrawl_scrape', {
            url: websiteUrl,
            formats: ['markdown'],
            onlyMainContent: true
          });
          
          return result;
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to scrape website for ${competitor}`, { error });
    }
    
    return null;
  }

  /**
   * Identify market trends
   */
  private async identifyMarketTrends(context: MarketResearchContext): Promise<MarketTrend[]> {
    const trendsQuery = this.buildTrendsQuery(context);
    const searchResults = await this.performTavilySearch(trendsQuery);
    
    return this.parseTrends(searchResults, context);
  }

  /**
   * Identify market opportunities
   */
  private async identifyOpportunities(
    context: MarketResearchContext,
    result: MarketResearchResult
  ): Promise<MarketOpportunity[]> {
    // Analyze gaps in competitor offerings and market trends
    return [
      {
        opportunity: 'Market Gap Analysis',
        description: 'Identified gaps in current market offerings',
        marketSize: 'To be determined',
        difficulty: 'medium',
        timeToMarket: '6-12 months',
        requiredResources: ['Development team', 'Marketing budget']
      }
    ];
  }

  /**
   * Identify market threats
   */
  private async identifyThreats(
    context: MarketResearchContext,
    result: MarketResearchResult
  ): Promise<MarketThreat[]> {
    return [
      {
        threat: 'Competitive Pressure',
        description: 'Increasing competition in the market',
        probability: 'high',
        impact: 'medium',
        mitigationStrategies: ['Differentiation', 'Innovation', 'Customer loyalty programs']
      }
    ];
  }

  /**
   * Generate strategic recommendations
   */
  private async generateRecommendations(
    context: MarketResearchContext,
    result: MarketResearchResult
  ): Promise<MarketRecommendation[]> {
    return [
      {
        category: 'Market Entry',
        recommendation: 'Focus on underserved market segments',
        rationale: 'Analysis shows gaps in current offerings',
        priority: 'high',
        timeline: '3-6 months',
        expectedOutcome: 'Increased market share'
      }
    ];
  }

  /**
   * Build market overview search query
   */
  private buildMarketOverviewQuery(context: MarketResearchContext): string {
    const parts = ['market analysis'];
    
    if (context.industry) parts.push(context.industry);
    if (context.targetMarket) parts.push(context.targetMarket);
    
    parts.push('market size', 'growth rate', '2024');
    
    return parts.join(' ');
  }

  /**
   * Build trends search query
   */
  private buildTrendsQuery(context: MarketResearchContext): string {
    const parts = ['market trends', '2024'];
    
    if (context.industry) parts.push(context.industry);
    if (context.targetMarket) parts.push(context.targetMarket);
    
    return parts.join(' ');
  }

  /**
   * Parse market overview from search results
   */
  private parseMarketOverview(searchResults: any, context: MarketResearchContext): MarketOverview {
    // This would use AI to parse search results into structured data
    return {
      marketSize: 'Analysis from search results',
      growthRate: 'Growth rate from research',
      keySegments: ['Segment 1', 'Segment 2'],
      majorPlayers: ['Player 1', 'Player 2'],
      marketDynamics: ['Dynamic 1', 'Dynamic 2'],
      regulatoryEnvironment: ['Regulation 1', 'Regulation 2']
    };
  }

  /**
   * Parse competitor data
   */
  private parseCompetitorData(
    competitor: string, 
    searchResults: any, 
    websiteData: any
  ): CompetitorAnalysis {
    return {
      name: competitor,
      website: 'Website from search results',
      description: `${competitor} analysis from research`,
      marketShare: 'Market share from analysis',
      strengths: ['Strength 1', 'Strength 2'],
      weaknesses: ['Weakness 1', 'Weakness 2'],
      products: [],
      pricing: [],
      marketingStrategy: ['Strategy 1', 'Strategy 2'],
      recentNews: []
    };
  }

  /**
   * Parse trends from search results
   */
  private parseTrends(searchResults: any, context: MarketResearchContext): MarketTrend[] {
    return [
      {
        trend: 'Digital Transformation',
        description: 'Increasing adoption of digital technologies',
        impact: 'high',
        timeframe: '2024-2026',
        drivers: ['Technology advancement', 'Consumer demand'],
        implications: ['Market disruption', 'New opportunities']
      }
    ];
  }

  /**
   * Create fallback market overview
   */
  private createFallbackMarketOverview(context: MarketResearchContext): MarketOverview {
    return {
      marketSize: 'Data unavailable',
      growthRate: 'Data unavailable',
      keySegments: ['Analysis unavailable'],
      majorPlayers: ['Data unavailable'],
      marketDynamics: ['Analysis unavailable'],
      regulatoryEnvironment: ['Data unavailable']
    };
  }

  /**
   * Create fallback competitor analysis
   */
  private createFallbackCompetitorAnalysis(competitor: string): CompetitorAnalysis {
    return {
      name: competitor,
      website: 'Unknown',
      description: 'Analysis unavailable',
      marketShare: 'Unknown',
      strengths: ['Analysis unavailable'],
      weaknesses: ['Analysis unavailable'],
      products: [],
      pricing: [],
      marketingStrategy: ['Analysis unavailable'],
      recentNews: []
    };
  }

  /**
   * Generate cache key for request
   */
  private generateCacheKey(request: SynthesisRequest): string {
    const key = `market-${request.operation}-${JSON.stringify(request.context)}`;
    return Buffer.from(key).toString('base64').substring(0, 32);
  }

  /**
   * Check if cache entry is valid
   */
  private isCacheValid(cacheKey: string): boolean {
    if (!this.cache.has(cacheKey)) return false;
    
    const expiry = this.cacheExpiry.get(cacheKey);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(cacheKey);
      this.cacheExpiry.delete(cacheKey);
      return false;
    }
    
    return true;
  }

  /**
   * Cache result with custom expiry
   */
  private cacheResult(cacheKey: string, result: any, expiryMinutes: number = 30): void {
    this.cache.set(cacheKey, result);
    this.cacheExpiry.set(cacheKey, Date.now() + (expiryMinutes * 60 * 1000));
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    this.cache.clear();
    this.cacheExpiry.clear();
    this.logger.info('Market Research Agent cleaned up');
  }
}

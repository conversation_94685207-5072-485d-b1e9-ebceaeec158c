/**
 * Technical Research Agent
 * 
 * Specializes in technical documentation research, library analysis,
 * and code-related investigations using Context7 and other technical resources.
 */

import { Agent } from '@mastra/core';
import { Logger } from '@mastra/loggers';
import { MCPClient } from '@mastra/mcp';
import { SynthesisRequest, SynthesisResponse } from '../synthesis/router.js';

export interface TechnicalResearchContext {
  libraries?: string[];
  technologies?: string[];
  frameworks?: string[];
  programmingLanguages?: string[];
  architecturePatterns?: string[];
  codeExamples?: boolean;
  documentationDepth?: 'basic' | 'detailed' | 'comprehensive';
}

export interface TechnicalResearchResult {
  libraries: LibraryAnalysis[];
  codeExamples: CodeExample[];
  bestPractices: string[];
  architectureRecommendations: ArchitectureRecommendation[];
  compatibilityMatrix: CompatibilityInfo[];
  implementationGuide: ImplementationStep[];
  sources: string[];
}

export interface LibraryAnalysis {
  name: string;
  version: string;
  description: string;
  features: string[];
  pros: string[];
  cons: string[];
  useCases: string[];
  documentation: string;
  examples: CodeExample[];
  popularity: number;
  maintenance: 'active' | 'maintained' | 'deprecated';
}

export interface CodeExample {
  title: string;
  description: string;
  code: string;
  language: string;
  framework?: string;
  complexity: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
}

export interface ArchitectureRecommendation {
  pattern: string;
  description: string;
  benefits: string[];
  tradeoffs: string[];
  applicability: string[];
  implementation: string;
}

export interface CompatibilityInfo {
  library1: string;
  library2: string;
  compatible: boolean;
  notes: string;
  alternativeApproach?: string;
}

export interface ImplementationStep {
  step: number;
  title: string;
  description: string;
  code?: string;
  dependencies?: string[];
  estimatedTime: string;
}

/**
 * Technical Research Agent - Specialized for technical documentation and analysis
 */
export class TechnicalResearchAgent extends Agent {
  private mcpClient: MCPClient;
  private logger: Logger;
  private cache = new Map<string, any>();
  private cacheExpiry = new Map<string, number>();

  constructor(mcpClient: MCPClient, logger: Logger) {
    super({
      name: 'TechnicalResearchAgent',
      description: 'Specialized agent for technical documentation research and library analysis',
      instructions: `
        You are a technical research specialist focused on:
        1. Library and framework analysis using Context7 documentation
        2. Code example generation and best practices
        3. Architecture pattern recommendations
        4. Compatibility analysis between technologies
        5. Implementation guidance and step-by-step instructions
        
        Always provide practical, actionable insights with code examples.
        Prioritize current, well-maintained libraries and proven patterns.
        Include compatibility considerations and migration paths.
      `
    });
    
    this.mcpClient = mcpClient;
    this.logger = logger;
  }

  /**
   * Synthesize technical research request
   */
  async synthesize(request: SynthesisRequest): Promise<TechnicalResearchResult> {
    this.logger.info('Starting technical research synthesis', { 
      operation: request.operation,
      tools: request.tools 
    });

    const context = request.context as TechnicalResearchContext;
    const cacheKey = this.generateCacheKey(request);

    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      this.logger.info('Returning cached technical research result');
      return this.cache.get(cacheKey);
    }

    try {
      const result: TechnicalResearchResult = {
        libraries: [],
        codeExamples: [],
        bestPractices: [],
        architectureRecommendations: [],
        compatibilityMatrix: [],
        implementationGuide: [],
        sources: []
      };

      // Step 1: Library Analysis using Context7
      if (context.libraries?.length) {
        result.libraries = await this.analyzeLibraries(context.libraries, context);
      }

      // Step 2: Generate Code Examples
      if (context.codeExamples) {
        result.codeExamples = await this.generateCodeExamples(context, result.libraries);
      }

      // Step 3: Architecture Recommendations
      if (context.architecturePatterns?.length) {
        result.architectureRecommendations = await this.generateArchitectureRecommendations(
          context.architecturePatterns, 
          context
        );
      }

      // Step 4: Compatibility Analysis
      if (context.libraries && context.libraries.length > 1) {
        result.compatibilityMatrix = await this.analyzeCompatibility(context.libraries);
      }

      // Step 5: Implementation Guide
      result.implementationGuide = await this.generateImplementationGuide(context, result);

      // Step 6: Best Practices
      result.bestPractices = await this.generateBestPractices(context, result);

      // Cache the result
      this.cacheResult(cacheKey, result);

      this.logger.info('Technical research synthesis completed', {
        librariesAnalyzed: result.libraries.length,
        codeExamples: result.codeExamples.length,
        architectureRecommendations: result.architectureRecommendations.length
      });

      return result;

    } catch (error) {
      this.logger.error('Error in technical research synthesis', { error });
      throw error;
    }
  }

  /**
   * Analyze libraries using Context7 documentation
   */
  private async analyzeLibraries(
    libraries: string[], 
    context: TechnicalResearchContext
  ): Promise<LibraryAnalysis[]> {
    const analyses: LibraryAnalysis[] = [];

    for (const library of libraries) {
      try {
        // Use Context7 to resolve library ID and get documentation
        const libraryId = await this.resolveLibraryId(library);
        if (libraryId) {
          const docs = await this.getLibraryDocs(libraryId, context.documentationDepth);
          const analysis = await this.parseLibraryDocumentation(library, docs);
          analyses.push(analysis);
        }
      } catch (error) {
        this.logger.warn(`Failed to analyze library ${library}`, { error });
        // Add basic analysis with fallback information
        analyses.push(this.createFallbackLibraryAnalysis(library));
      }
    }

    return analyses;
  }

  /**
   * Resolve library ID using Context7
   */
  private async resolveLibraryId(libraryName: string): Promise<string | null> {
    try {
      const tools = await this.mcpClient.getTools();
      const context7Tools = tools.context7;
      
      if (context7Tools && context7Tools['resolve-library-id']) {
        const result = await this.mcpClient.callTool('context7', 'resolve-library-id', {
          libraryName
        });
        
        return result.libraryId || null;
      }
    } catch (error) {
      this.logger.warn(`Failed to resolve library ID for ${libraryName}`, { error });
    }
    
    return null;
  }

  /**
   * Get library documentation using Context7
   */
  private async getLibraryDocs(libraryId: string, depth?: string): Promise<any> {
    try {
      const tools = await this.mcpClient.getTools();
      const context7Tools = tools.context7;
      
      if (context7Tools && context7Tools['get-library-docs']) {
        const tokens = depth === 'comprehensive' ? 20000 : depth === 'detailed' ? 15000 : 10000;
        
        const result = await this.mcpClient.callTool('context7', 'get-library-docs', {
          context7CompatibleLibraryID: libraryId,
          tokens
        });
        
        return result;
      }
    } catch (error) {
      this.logger.warn(`Failed to get library docs for ${libraryId}`, { error });
    }
    
    return null;
  }

  /**
   * Parse library documentation into structured analysis
   */
  private async parseLibraryDocumentation(libraryName: string, docs: any): Promise<LibraryAnalysis> {
    // This would use AI to parse the documentation and extract structured information
    // For now, return a structured placeholder
    return {
      name: libraryName,
      version: 'latest',
      description: `${libraryName} library analysis from Context7 documentation`,
      features: ['Feature extraction from docs'],
      pros: ['Pros extracted from documentation'],
      cons: ['Cons identified from analysis'],
      useCases: ['Use cases from documentation'],
      documentation: 'Context7 documentation available',
      examples: [],
      popularity: 8,
      maintenance: 'active'
    };
  }

  /**
   * Create fallback library analysis when Context7 is unavailable
   */
  private createFallbackLibraryAnalysis(libraryName: string): LibraryAnalysis {
    return {
      name: libraryName,
      version: 'unknown',
      description: `${libraryName} - analysis unavailable (Context7 offline)`,
      features: ['Analysis unavailable'],
      pros: ['Unable to determine'],
      cons: ['Analysis unavailable'],
      useCases: ['Unable to determine'],
      documentation: 'Not available',
      examples: [],
      popularity: 0,
      maintenance: 'active'
    };
  }

  /**
   * Generate code examples based on context and library analysis
   */
  private async generateCodeExamples(
    context: TechnicalResearchContext,
    libraries: LibraryAnalysis[]
  ): Promise<CodeExample[]> {
    const examples: CodeExample[] = [];

    // Generate examples for each library
    for (const library of libraries) {
      examples.push({
        title: `Basic ${library.name} Setup`,
        description: `Basic setup and initialization for ${library.name}`,
        code: `// Basic ${library.name} setup\nimport ${library.name} from '${library.name}';\n\n// Initialize and use`,
        language: context.programmingLanguages?.[0] || 'javascript',
        framework: context.frameworks?.[0],
        complexity: 'beginner',
        tags: [library.name, 'setup', 'basic']
      });
    }

    return examples;
  }

  /**
   * Generate architecture recommendations
   */
  private async generateArchitectureRecommendations(
    patterns: string[],
    context: TechnicalResearchContext
  ): Promise<ArchitectureRecommendation[]> {
    return patterns.map(pattern => ({
      pattern,
      description: `${pattern} architecture pattern analysis`,
      benefits: [`Benefits of ${pattern} pattern`],
      tradeoffs: [`Tradeoffs for ${pattern} pattern`],
      applicability: [`When to use ${pattern}`],
      implementation: `Implementation guidance for ${pattern}`
    }));
  }

  /**
   * Analyze compatibility between libraries
   */
  private async analyzeCompatibility(libraries: string[]): Promise<CompatibilityInfo[]> {
    const compatibility: CompatibilityInfo[] = [];

    for (let i = 0; i < libraries.length; i++) {
      for (let j = i + 1; j < libraries.length; j++) {
        compatibility.push({
          library1: libraries[i],
          library2: libraries[j],
          compatible: true, // Would be determined by actual analysis
          notes: `Compatibility analysis between ${libraries[i]} and ${libraries[j]}`
        });
      }
    }

    return compatibility;
  }

  /**
   * Generate implementation guide
   */
  private async generateImplementationGuide(
    context: TechnicalResearchContext,
    result: TechnicalResearchResult
  ): Promise<ImplementationStep[]> {
    const steps: ImplementationStep[] = [
      {
        step: 1,
        title: 'Project Setup',
        description: 'Initialize project and install dependencies',
        estimatedTime: '15 minutes'
      },
      {
        step: 2,
        title: 'Configuration',
        description: 'Configure libraries and frameworks',
        estimatedTime: '30 minutes'
      },
      {
        step: 3,
        title: 'Implementation',
        description: 'Implement core functionality',
        estimatedTime: '2-4 hours'
      }
    ];

    return steps;
  }

  /**
   * Generate best practices
   */
  private async generateBestPractices(
    context: TechnicalResearchContext,
    result: TechnicalResearchResult
  ): Promise<string[]> {
    return [
      'Follow semantic versioning for dependencies',
      'Implement proper error handling',
      'Use TypeScript for better type safety',
      'Write comprehensive tests',
      'Document your API thoroughly'
    ];
  }

  /**
   * Generate cache key for request
   */
  private generateCacheKey(request: SynthesisRequest): string {
    const key = `tech-${request.operation}-${JSON.stringify(request.context)}`;
    return Buffer.from(key).toString('base64').substring(0, 32);
  }

  /**
   * Check if cache entry is valid
   */
  private isCacheValid(cacheKey: string): boolean {
    if (!this.cache.has(cacheKey)) return false;
    
    const expiry = this.cacheExpiry.get(cacheKey);
    if (!expiry || Date.now() > expiry) {
      this.cache.delete(cacheKey);
      this.cacheExpiry.delete(cacheKey);
      return false;
    }
    
    return true;
  }

  /**
   * Cache result with expiry
   */
  private cacheResult(cacheKey: string, result: any): void {
    this.cache.set(cacheKey, result);
    this.cacheExpiry.set(cacheKey, Date.now() + (30 * 60 * 1000)); // 30 minutes
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    this.cache.clear();
    this.cacheExpiry.clear();
    this.logger.info('Technical Research Agent cleaned up');
  }
}

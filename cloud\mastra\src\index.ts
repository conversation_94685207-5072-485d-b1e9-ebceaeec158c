/**
 * Guidant Strategic Agent Synthesis Architecture
 * 
 * Implements selective agent synthesis for complex operations while preserving
 * direct calls for simple operations. Integrates multiple MCP servers for
 * comprehensive research and analysis capabilities.
 */

import { <PERSON><PERSON> } from '@mastra/core';
import { PinoLogger } from '@mastra/loggers';
import { MCPClient } from '@mastra/mcp';
import { createServer } from './server.js';
import { AgentSynthesisRouter } from './synthesis/router.js';
import { TechnicalResearchAgent } from './agents/technical-research.js';
import { MarketResearchAgent } from './agents/market-research.js';
import { UIUXResearchAgent } from './agents/uiux-research.js';
import { GeneralResearchAgent } from './agents/general-research.js';
import { loadConfig } from './utils/config.js';
import { createMCPServerConfig } from './mcp/server-config.js';

// Initialize logger
const logger = new PinoLogger({
  name: 'Guidant-Strategic-Synthesis',
  level: process.env.LOG_LEVEL || 'info'
});

// Create MCP Client with comprehensive server configuration
const mcpClient = new MCPClient({
  id: 'guidant-strategic-synthesis',
  servers: createMCPServerConfig(),
  timeout: 30000,
  logger: logger
});

// Initialize specialized synthesis agents
const technicalAgent = new TechnicalResearchAgent(mcpClient, logger);
const marketAgent = new MarketResearchAgent(mcpClient, logger);
const uiuxAgent = new UIUXResearchAgent(mcpClient, logger);
const generalAgent = new GeneralResearchAgent(mcpClient, logger);

// Create agent synthesis router
const synthesisRouter = new AgentSynthesisRouter({
  technicalAgent,
  marketAgent,
  uiuxAgent,
  generalAgent
}, logger);

// Create Mastra instance with synthesis capabilities
const config = loadConfig();
const mastra = new Mastra({
  agents: {
    technicalResearch: technicalAgent,
    marketResearch: marketAgent,
    uiuxResearch: uiuxAgent,
    generalResearch: generalAgent
  },
  logger: logger
});

// Attach synthesis router and MCP client to Mastra instance
(mastra as any).synthesisRouter = synthesisRouter;
(mastra as any).mcpClient = mcpClient;

/**
 * Main application initialization
 */
async function main(): Promise<void> {
  logger.info('=== Initializing Guidant Strategic Agent Synthesis Architecture ===');
  
  try {
    // Step 1: Initialize MCP Client and get available tools
    logger.info('Step 1: Initializing MCP Client...');
    let mcpTools: Record<string, any> = {};
    
    try {
      await mcpClient.connect();
      mcpTools = await mcpClient.getTools();
      logger.info(`MCP Tools available: ${Object.keys(mcpTools).length} tools from ${Object.keys(mcpClient.servers).length} servers`);
      
      // Log available tools by server
      for (const [serverName, tools] of Object.entries(mcpTools)) {
        logger.info(`${serverName}: ${Array.isArray(tools) ? tools.length : Object.keys(tools).length} tools`);
      }
    } catch (mcpError) {
      logger.warn('MCP Client initialization failed, running in fallback mode', { error: mcpError });
      // Continue without MCP tools - agents have fallback implementations
    }

    // Step 2: Initialize synthesis router with available tools
    logger.info('Step 2: Initializing Agent Synthesis Router...');
    await synthesisRouter.initialize(mcpTools);
    
    // Step 3: Validate configuration and agents
    logger.info('Step 3: Validating configuration and agents...');
    logger.info(`Config loaded: ${!!config}`);
    logger.info(`Available agents: ${Object.keys(mastra.agents || {}).join(', ')}`);
    
    // Step 4: Create and start server
    logger.info('Step 4: Creating Express server...');
    const server = createServer(mastra, config, synthesisRouter);
    
    const port = config.server?.port || 8080;
    const host = config.server?.host || '0.0.0.0';
    
    // Step 5: Set up graceful shutdown
    logger.info('Step 5: Setting up graceful shutdown handlers...');
    setupGracefulShutdown();
    
    // Step 6: Start server
    logger.info(`Step 6: Starting server on ${host}:${port}...`);
    
    server.listen(port, host, () => {
      logger.info('=== SERVER STARTED SUCCESSFULLY ===', {
        host,
        port,
        availableAgents: Object.keys(mastra.agents || {}),
        mcpServers: Object.keys(mcpClient.servers),
        mcpToolsCount: Object.keys(mcpTools).length,
        healthEndpoint: `http://${host}:${port}/health`,
        synthesisEndpoint: `http://${host}:${port}/api/synthesis`
      });
      
      // Periodic health logging
      setInterval(() => {
        logger.info('Server health check', {
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage()
        });
      }, 60000); // Every minute
    });
    
  } catch (error) {
    logger.error('Failed to initialize Strategic Agent Synthesis Architecture', { error });
    process.exit(1);
  }
}

/**
 * Set up graceful shutdown handlers
 */
function setupGracefulShutdown(): void {
  const shutdown = async (signal: string) => {
    logger.info(`Received ${signal}, shutting down gracefully...`);
    
    try {
      // Close MCP connections
      if (mcpClient) {
        await mcpClient.disconnect();
        logger.info('MCP Client disconnected');
      }
      
      // Cleanup agents
      await Promise.all([
        technicalAgent.cleanup?.(),
        marketAgent.cleanup?.(),
        uiuxAgent.cleanup?.(),
        generalAgent.cleanup?.()
      ].filter(Boolean));
      
      logger.info('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during graceful shutdown', { error });
      process.exit(1);
    }
  };

  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
}

// Handle unhandled errors
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Promise Rejection', { reason, promise });
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error });
  process.exit(1);
});

// Start the application
main().catch((error) => {
  logger.error('Unhandled error during startup', { error });
  process.exit(1);
});

// Export for external use
export { mastra as default, mcpClient, synthesisRouter };
export { createGuidantIntegration, GuidantMastraIntegration } from './integration/index.js';
